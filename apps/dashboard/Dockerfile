#### BASE STAGE
#### Installs moon.

FROM node:latest AS base
WORKDIR /app

# Install moon binary
RUN npm install -g @moonrepo/cli

#### SKELETON STAGE
#### Scaffolds repository skeleton structures.

FROM base AS skeleton

# Copy entire repository and scaffold
COPY . .
RUN moon docker scaffold dashboard

#### BUILD STAGE
#### Builds the project.

FROM base AS build

# Copy workspace configs
COPY --from=skeleton /app/.moon/docker/workspace .

# Install dependencies
RUN moon docker setup

# Copy project sources
COPY --from=skeleton /app/.moon/docker/sources .
COPY tsconfig.json .
COPY tsconfig.base.json .

ENV NEXT_PUBLIC_PRODUCTION_BACKEND_URL=$NEXT_PUBLIC_PRODUCTION_BACKEND_URL
ENV NEXT_PUBLIC_DEVELOPMENT_BACKEND_URL=$NEXT_PUBLIC_DEVELOPMENT_BACKEND_URL
ENV NODE_ENV=$NODE_ENV

# Build the project
RUN moon run dashboard:build

# Prune extraneous dependencies
RUN moon docker prune

#### START STAGE
#### Runs the project.

FROM base AS start

# Copy built sources
COPY --from=build /app /app

