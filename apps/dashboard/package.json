{"dependencies": {"@mass/components": "workspace:*", "@mass/icons": "workspace:*", "@mass/tailwind": "workspace:*", "clsx": "^2.1.1", "next": "15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "name": "@mass/dashboard", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbopack", "start": "next start"}, "version": "0.0.0"}