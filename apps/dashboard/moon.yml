language: "typescript"
type: "library"
platform: "bun"

tasks:
  dev:
    local: true
    command: "bun run next dev --turbopack"
    deps: ["icons:dev", "components:dev"]
    inputs:
      - "src/**/*"
    outputs:
      - ".next/**"
  build:
    command: "bun run next build"
    deps: ["icons:build", "components:build"]
    inputs:
      - "src/**/*"
    outputs:
      - ".next/**"
  start:
    local: true
    command: "bun run next start"
    deps: ["dashboard:build"]
    inputs:
      - "src/**/*"
    outputs:
      - ".next/**"
  docker:
    local: true
    command: "bun run next start"
    inputs:
      - ".next/**"