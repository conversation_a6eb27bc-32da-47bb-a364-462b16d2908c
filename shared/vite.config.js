import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import dts from 'vite-plugin-dts';
import { glob } from 'glob';
const bannerPlugin = () => {
    return {
        name: 'banner',
        generateBundle(_, bundle) {
            for (const fileName in bundle) {
                if (fileName.endsWith('.js') || fileName.endsWith('.mjs')) {
                    const file = bundle[fileName];
                    if (file.type === 'chunk') {
                        file.code = '"use client";\n' + file.code;
                    }
                }
            }
        },
    };
};
const entries = Object.fromEntries(glob.sync([
    './index.ts',
    './**/index.ts',
    './**/*.ts',
    './**/*.tsx',
], {
    ignore: [
        './**/*.d.ts',
        './**/*.test.ts',
        './**/*.test.tsx',
        './dist/**',
        './node_modules/**',
        './vite.config.ts'
    ],
}).map(file => [
    file.replace(/\.(tsx?|jsx?)$/, ''),
    path.resolve(__dirname, file)
]));
export default defineConfig({
    plugins: [
        react({
            jsxRuntime: 'automatic',
            jsxImportSource: 'react',
            babel: {
                plugins: []
            }
        }),
        dts({
            outDir: 'dist/types',
            include: ['index.ts', './**/*.ts', './**/*.tsx'],
            exclude: ['**/*.test.ts', '**/*.test.tsx', 'node_modules/**', 'dist/**', 'vite.config.ts'],
            rollupTypes: true,
            insertTypesEntry: true,
            clearPureImport: true
        }),
        bannerPlugin()
    ],
    resolve: {
        alias: [
            { find: '@', replacement: path.resolve(__dirname, '.') },
        ]
    },
    build: {
        target: 'es2020',
        outDir: 'dist',
        emptyOutDir: true,
        sourcemap: true,
        lib: {
            entry: entries,
            formats: ['es'],
            fileName: (_format, entryName) => `${entryName}.js`,
        },
        rollupOptions: {
            external: [
                'react',
                'react-dom',
                'react-i18next',
                'i18next',
                /^@radix-ui\//,
                /^@tanstack\//,
                /^@hookform\//,
                /^@mdx-js\//,
                'date-fns',
                'zod',
                'virtual:i18next-loader',
                /^virtual:/,
            ],
            output: {
                preserveModules: true,
                preserveModulesRoot: './',
                entryFileNames: '[name].js',
                chunkFileNames: '[name].js',
                globals: {
                    react: 'React',
                    'react-dom': 'ReactDOM'
                },
            }
        },
        assetsInlineLimit: 0,
        copyPublicDir: true,
    }
});
