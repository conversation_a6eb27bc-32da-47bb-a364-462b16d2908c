{"devDependencies": {"@ozaco/cli": "^0.0.12", "@types/react": "^19.1.8", "clsx": "^2.1.1", "react": "^19.1.0", "typescript": "^5.8.3"}, "exports": {".": {"default": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts"}}, "files": ["dist"], "name": "@mass/icons", "peerDependencies": {"clsx": ">= 2.1.1", "react": ">= 19.1.0", "typescript": ">= 5.8.3"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "tsx-exports": ["default"], "type": "module", "version": "0.0.0"}