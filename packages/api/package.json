{"devDependencies": {"@ozaco/cli": "^0.0.12", "typescript": "^5.8.3"}, "exports": {"./dashboard": {"default": "./dist/dashboard.js", "source": "./src/dashboard/index.ts", "types": "./dist/dashboard.d.ts"}, "./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "files": ["dist"], "name": "@mass/api", "peerDependencies": {"typescript": ">= 5.8.3"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "type": "module", "version": "0.0.0"}