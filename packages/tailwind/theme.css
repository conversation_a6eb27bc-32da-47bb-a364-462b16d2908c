@theme {
  --*: initial;

  --spacing: 0.125rem;

  --breakpoint-xs: 26.25rem;
  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --aspect-video: 16 / 9;

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --radius-full: 100%;
  --radius-max: 2.097rem; /* 33554400px max css value */
  --radius-l1: 1.5rem; /* 24px */
  --radius-l2: 1rem; /* 16px */
  --radius-c1: 0.75rem; /* 12px */
  --radius-c2: 1.125rem; /* 18px */


  --animate-beam: beam 3s ease-in-out infinite;

  --color-transparent: transparent;
}
