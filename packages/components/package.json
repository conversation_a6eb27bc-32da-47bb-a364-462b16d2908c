{"devDependencies": {"@mass/icons": "workspace:*", "@ozaco/cli": "^0.0.12", "@types/react": "^19.1.8", "clsx": "^2.1.1", "motion": "^12.23.0", "next": "15.3.4", "next-view-transitions": "^0.3.4", "react": "^19.1.0", "typescript": "^5.8.3"}, "exports": {"./dashboard": {"default": "./dist/dashboard.js", "source": "./src/dashboard/index.ts", "types": "./dist/dashboard.d.ts"}, "./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "files": ["dist"], "name": "@mass/components", "peerDependencies": {"@mass/icons": "workspace:*", "clsx": ">= 2.1.1", "motion": ">= 12.16.0", "next": ">= 15.3.2", "next-view-transitions": ">= 0.3.4", "react": ">= 19.1.0", "typescript": ">= 5.8.3"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "tsx-exports": ["dashboard", "shared"], "type": "module", "version": "0.0.0"}