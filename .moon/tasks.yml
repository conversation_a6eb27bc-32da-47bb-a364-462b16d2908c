$schema: "https://moonrepo.dev/schemas/tasks.json"

implicitInputs:
  - "package.json"
  - "tsconfig.json"

fileGroups:
  configs:
    - "*.config.{js,cjs,mjs,ts}"
    - "*.json"
    - "*.yml"
    - "*.yaml"
  sources:
    - "src/**/*"
    - "packages/**/*"
    - "plugins/**/*"
    - "types/**/*"
  tests:
    - "tests/**/*"
    - "**/__tests__/**/*"
  assets:
    - "assets/**/*"
    - "images/**/*"
    - "static/**/*"
    - "**/*.{scss,css}"

tasks:
  check:
    command: "bun x @biomejs/biome check --no-errors-on-unmatched --files-ignore-unknown=true ."
    platform: "system"
    options:
      affectedFiles: true
      runInCI: false
  apply:
    command: "bun x @biomejs/biome check --write --no-errors-on-unmatched --files-ignore-unknown=true ."
    platform: "system"
    options:
      affectedFiles: true
      runInCI: false
  apply-unsafe:
    command: "bun x @biomejs/biome check --write --unsafe --no-errors-on-unmatched --files-ignore-unknown=true ."
    platform: "system"
    options:
      affectedFiles: true
      runInCI: false
  clean:
    command: "rm -rf dist && rm -rf .out"
    local: true
    options:
      cache: false
      persistent: false
      runInCI: false
